<?php
// Fichier dédié à l'analyse et la correction des codes-barres
// Ce fichier ne doit générer AUCUN output HTML

// Démarrer la capture de sortie pour éviter tout output accidentel
ob_start();

require ("_dir.inc.php");
require ("_profil.inc.php");
require ($DIR . "_session.inc.php");


ob_end_clean();

$action = $_GET['action'] ?? 'analyse';


function recalculerChiffreControleEAN($code_barre) {
    $code_sans_controle = substr($code_barre, 0, -1);
    if (strlen($code_sans_controle) != 7 && strlen($code_sans_controle) != 12) {
        return null;
    }
    $poids_array = (strlen($code_sans_controle) == 12 ? array(0 => 1, 1 => 3) : array(0 => 3, 1 => 1));
    $somme = 0;
    for ($i = 0; $i < strlen($code_sans_controle); $i++) {
        $poids = $i % 2;
        $cur_num = (int)$code_sans_controle[$i];
        $somme += $cur_num * $poids_array[$poids];
    }
    $chiffre_controle = (10 - ($somme % 10)) * ($somme % 10 ? 1 : 0);
    return $code_sans_controle . $chiffre_controle;
}

try {
    
    while (ob_get_level()) {
        ob_end_clean();
    }

    $bdd = PDO_etendu::getInstance();

    // Analyser TOUS les articles pour générer le CSV complet
    $query_all = "SELECT id_article, ref_article, code_barre_1 FROM articles WHERE code_barre_1 IS NOT NULL AND code_barre_1 != '' AND LENGTH(code_barre_1) IN (8, 13) AND dispo = 1 ORDER BY ref_article";
    $result_all = $bdd->query($query_all);

    $tous_resultats = array();
    $articles_corriges = array();
    $articles_erreur_format = array();
    $total_traites = 0;
    $total_corriges = 0;

    
    if ($action === 'correction') {
        $bdd->beginTransaction();
    }

    while ($article = $result_all->fetchObject()) {
        $total_traites++;
        $code_actuel = $article->code_barre_1;
        $code_corrige = recalculerChiffreControleEAN($code_actuel);

        $statut = 'Correct';
        if ($code_corrige === null) {
            $statut = 'Erreur format';
        } elseif ($code_actuel !== $code_corrige) {
            $statut = 'Erreur chiffre controle';
        }

        // Traitement selon le mode
        if ($action === 'correction') {
            // Mode correction : corriger en base et préparer le rapport
            if ($code_corrige === null) {
                $articles_erreur_format[] = array(
                    'ref_article' => $article->ref_article,
                    'code_actuel' => $code_actuel,
                    'erreur' => 'Format invalide'
                );
            } elseif ($code_actuel !== $code_corrige) {
                try {
                    $query_update = "UPDATE articles SET code_barre_1 = :code_corrige WHERE id_article = :id_article";
                    $stmt = $bdd->prepare($query_update);
                    $stmt->execute([
                        ':code_corrige' => $code_corrige,
                        ':id_article' => $article->id_article
                    ]);

                    $articles_corriges[] = array(
                        'ref_article' => $article->ref_article,
                        'code_ancien' => $code_actuel,
                        'code_nouveau' => $code_corrige
                    );
                    $total_corriges++;

                } catch (Exception $e) {
                    $articles_erreur_format[] = array(
                        'ref_article' => $article->ref_article,
                        'code_actuel' => $code_actuel,
                        'erreur' => 'Erreur SQL: ' . $e->getMessage()
                    );
                }
            }
        } else {
            // Mode analyse : ajouter SEULEMENT les résultats avec des erreurs pour le CSV
            if ($statut !== 'Correct') {
                $tous_resultats[] = array(
                    'ref_article' => $article->ref_article,
                    'code_actuel' => $code_actuel,
                    'code_corrige' => $code_corrige ?: $code_actuel,
                    'statut' => $statut
                );
            }
        }
    }

    // Si c'est une correction, valider la transaction
    if ($action === 'correction') {
        $bdd->commit();
    }

    // Générer et télécharger le fichier CSV selon le mode
    header("Content-type: text/csv; charset=ISO-8859-1");
    if ($action === 'correction') {
        header("Content-disposition: attachment; filename=\"rapport_correction_codes_barres_" . date("Ymd_His") . ".csv\"");
    } else {
        header("Content-disposition: attachment; filename=\"codes_barres_erreurs_" . date("Ymd_His") . ".csv\"");
    }
    header("Pragma: no-cache");
    header("Expires: 0");

    // Créer le contenu CSV selon le mode
    if ($action === 'correction') {
        // Mode correction : rapport détaillé
        $csv_content = "ref_article;code_ancien;code_nouveau;Statut;\n";

        // Ajouter les articles corrigés
        foreach ($articles_corriges as $article) {
            $csv_content .= '"' . str_replace('"', '""', $article['ref_article']) . '";';
            $csv_content .= '"' . str_replace('"', '""', $article['code_ancien']) . '";';
            $csv_content .= '"' . str_replace('"', '""', $article['code_nouveau']) . '";';
            $csv_content .= '"Correction effectuee"' . "\n";
        }

        // Ajouter les articles avec erreurs non corrigées
        foreach ($articles_erreur_format as $article) {
            $csv_content .= '"ERREUR";';
            $csv_content .= '"' . str_replace('"', '""', $article['ref_article']) . '";';
            $csv_content .= '"' . str_replace('"', '""', $article['code_actuel']) . '";';
            $csv_content .= '"";';
            $csv_content .= '"' . str_replace('"', '""', $article['erreur']) . '"' . "\n";
        }

        // Ajouter un résumé en fin de fichier
        $csv_content .= "\n";
        $csv_content .= '"RESUME";"Total traites";"' . $total_traites . '";"Total corriges";"' . $total_corriges . '"' . "\n";
        $csv_content .= '"RESUME";"Erreurs format";"' . count($articles_erreur_format) . '";"Taux correction";"' . ($total_traites > 0 ? round(($total_corriges / $total_traites) * 100, 2) : 0) . '%"' . "\n";

    } else {
        // Mode analyse : liste des erreurs seulement
        $csv_content = "ref_article;Code-barre;Correction;Statut;\n";

        foreach ($tous_resultats as $resultat) {
            $csv_content .= '"' . str_replace('"', '""', $resultat['ref_article']) . '";';
            $csv_content .= '"' . str_replace('"', '""', $resultat['code_actuel']) . '";';
            $csv_content .= '"' . str_replace('"', '""', $resultat['code_corrige']) . '";';
            $csv_content .= '"' . str_replace('"', '""', $resultat['statut']) . '"' . "\n";
        }
    }

    // Envoyer le CSV (conversion UTF-8 vers ISO-8859-1 pour Excel)
    echo mb_convert_encoding($csv_content, 'ISO-8859-1', 'UTF-8');
    
} catch (Exception $e) {
    // En cas d'erreur, annuler la transaction si nécessaire
    if ($action === 'correction' && isset($bdd) && $bdd->inTransaction()) {
        $bdd->rollback();
    }

    // En cas d'erreur, envoyer un CSV avec le message d'erreur
    header("Content-type: text/csv; charset=ISO-8859-1");
    if ($action === 'correction') {
        header("Content-disposition: attachment; filename=\"erreur_correction_codes_barres_" . date("Ymd_His") . ".csv\"");
        $message_erreur = "Erreur lors de la correction : " . $e->getMessage();
    } else {
        header("Content-disposition: attachment; filename=\"erreur_analyse_codes_barres_" . date("Ymd_His") . ".csv\"");
        $message_erreur = "Erreur lors de l'analyse : " . $e->getMessage();
    }
    header("Pragma: no-cache");
    header("Expires: 0");

    $csv_error = "Erreur\n";
    $csv_error .= '"' . str_replace('"', '""', $message_erreur) . '"' . "\n";
    echo mb_convert_encoding($csv_error, 'ISO-8859-1', 'UTF-8');
}

// Arrêter complètement l'exécution
exit();
?>
